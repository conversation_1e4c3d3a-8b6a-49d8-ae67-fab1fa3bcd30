import {Flex, Typography} from 'antd';
import styled from '@emotion/styled';
import {cx} from '@emotion/css';
import {CSSProperties, useMemo} from 'react';
import {overflowHiddenCss} from '@/styles/components';
import {howLongAgo} from '@/utils/date';

const Text = styled(Typography.Text)`
    font-size: 12px;
    line-height: 20px;
`;

interface Props {
    className?: string;
    color?: string;
    username?: string;
    time?: string | number | Date;
    style?: CSSProperties;
}

export default function PublishInfo({className, username, time, color = '#545454', style}: Props) {
    const timeStr = useMemo(
        () => {
            try {
                return howLongAgo(time);
            } catch (e) {
                return '0秒';
            }
        },
        [time]
    );
    return (
        <Flex className={cx(overflowHiddenCss, className)} style={style} align="center" gap={4}>
            <Text ellipsis style={{color}}>
                {username} 发布于 {timeStr}前
            </Text>
        </Flex>
    );
}
