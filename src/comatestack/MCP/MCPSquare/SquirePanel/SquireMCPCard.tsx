import {Flex, Typo<PERSON>, Tooltip, Divider} from 'antd';
import React, {useCallback} from 'react';
import {useNavigate} from 'react-router-dom';
import {But<PERSON>} from '@panda-design/components';
import {MCPDetailLink, MCPPlaygroundLink} from '@/links/mcp';
import {MCPCollectButton} from '@/components/MCP/MCPCollectButton';
import {MCPSubscribeButton} from '@/components/MCP/MCPSubscribeButton';
import {MCPServerBase} from '@/types/mcp/mcp';
import {apiPostViewCount} from '@/api/mcp';
import {getServerTypeText} from '@/components/MCP/MCPServerTypeTag';
import SvgEye from '@/icons/mcp/Eye';
// import SvgCase from '@/icons/mcp/Case';
// import SvgComment from '@/icons/mcp/Comment';
import TagGroup from '@/components/MCP/TagGroup';
import <PERSON><PERSON><PERSON> from '@/design/MCP/MCPCard';
import MC<PERSON>erverAvatar from '@/components/MCP/MCPServerAvatar';
import PublishInfo from '@/components/MCP/PublishInfo';
import {
    containerCss,
    hoverActionsStyle,
    DescriptionContainer,
    DescriptionText,
    EllipsisOverlay,
    // RatingText,
    cardContentStyle,
    protocolTextStyle,
    departmentTextStyle,
    dividerStyle,
    statsContainerStyle,
    iconStyle,
    formatCount,
    actionButtonStyle,
} from './SquireMCPCard.styles';

interface Props {
    server: MCPServerBase;
    refresh: () => void;
}
const SquireMCPCard = ({server, refresh}: Props) => {
    const {
        id,
        workspaceId,
        name,
        departmentName,
        serverSourceType,
        serverProtocolType,
        description,
        labels,
        icon,
        viewCount,
        favorite,
        publishTime,
        publishUser,
    } = server;
    const navigate = useNavigate();

    const handleClick = useCallback(
        async () => {
            await apiPostViewCount({mcpServerId: id});
            navigate(MCPDetailLink.toUrl({mcpId: id}));
        },
        [navigate, id]
    );
    const handleViewCountClick = useCallback(
        (e: React.MouseEvent) => {
            e.stopPropagation();
            navigate(MCPDetailLink.toUrl({mcpId: id, tab: 'overview'}));
        },
        [navigate, id]
    );
    const handlePlaygroundClick = useCallback(
        (e: React.MouseEvent) => {
            e.stopPropagation();
            window.open(MCPPlaygroundLink.toUrl({serverId: id}), '_blank');
        },
        [id]
    );

    return (
        <MCPCard vertical onClick={handleClick} className={containerCss}>
            <Flex gap={14} align="center">
                <MCPServerAvatar icon={icon} />
                <Flex vertical justify="space-between" style={cardContentStyle} gap={4}>
                    <Typography.Title level={4} ellipsis>{name}</Typography.Title>
                    <Flex justify="space-between" align="center" style={{width: '100%'}}>
                        <Flex align="center" gap={12}>
                            <Typography.Text style={protocolTextStyle}>
                                {getServerTypeText(serverSourceType)}
                            </Typography.Text>
                            <Typography.Text style={protocolTextStyle}>|</Typography.Text>
                            <Typography.Text style={protocolTextStyle}>{serverProtocolType}</Typography.Text>
                        </Flex>
                        {/* <RatingText score={rating}>{rating ? `${rating}分` : '暂无评分'}</RatingText> */}
                    </Flex>
                </Flex>
            </Flex>
            <Tooltip title={description || '暂无描述'} placement="top">
                <DescriptionContainer>
                    <DescriptionText>{description || '暂无描述'}</DescriptionText>
                    <EllipsisOverlay />
                </DescriptionContainer>
            </Tooltip>
            <Typography.Text style={departmentTextStyle}>{departmentName || '暂无部门信息'}</Typography.Text>
            <TagGroup
                labels={labels.map(label => ({id: label.id, label: label.labelValue}))}
                prefix={null}
                style={{flexShrink: 1, overflow: 'hidden'}}
                color="light-purple"
                gap={4}
            />
            <Divider style={dividerStyle} />
            <Flex justify="space-between" align="center">
                <Flex align="center" gap={12}>
                    <Tooltip title="浏览量">
                        <Flex align="center" gap={4} onClick={handleViewCountClick} className={statsContainerStyle}>
                            <SvgEye style={iconStyle} />
                            {formatCount(viewCount)}
                        </Flex>
                    </Tooltip>
                    {/* <Flex align="center" gap={4} onClick={e => e.stopPropagation()} className={statsContainerStyle}>
                        <SvgCase style={iconStyle} />
                        {formatCount(caseCount)}
                    </Flex>
                    <Flex align="center" gap={4} onClick={e => e.stopPropagation()} className={statsContainerStyle}>
                        <SvgComment style={iconStyle} />
                        {formatCount(commentCount)}
                    </Flex> */}
                </Flex>
                <PublishInfo username={publishUser} time={publishTime} />
            </Flex>
            <Flex align="center" justify="space-between" gap={10} className={`hover-actions ${hoverActionsStyle}`}>
                <MCPCollectButton refresh={refresh} favorite={favorite} serverId={id} style={actionButtonStyle} />
                <MCPSubscribeButton refresh={refresh} workspaceId={workspaceId} id={id} style={actionButtonStyle} />
                <Button type="primary" onClick={handlePlaygroundClick}>去MCP Playground使用</Button>
            </Flex>
        </MCPCard>
    );
};

export default SquireMCPCard;
