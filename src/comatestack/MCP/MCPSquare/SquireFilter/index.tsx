/* eslint-disable max-lines */
import {Flex, Form, Input, Select, Switch, Popover, Space} from 'antd';
import {SearchOutlined, DownOutlined} from '@ant-design/icons';
import styled from '@emotion/styled';
import TitleTabs from '@/components/MCP/TitleTabs';
import {MCPPlaygoundButton} from '@/components/MCP/MCPPlaygoundButton';
import {CreateMCPButton} from '@/components/MCP/CreateMCPButton';
import LabelsFilterContent from './LabelsFilterContent';

const StyleForm = styled(Form)`
    position: relative !important;
    .ant-5-form-css-var {
        --ant-5-form-item-margin-bottom: 0px !important;
    }
    label {
        color: #8F8F8F!important;
        gap: 16px !important;
        &: before{
            display: none !important;
        }
        &: after{
            content: '' !important;
            width: 1px !important;
            height: 16px !important;
            background: #D9D9D9 !important;
        }
    }
    .noLine {
        .ant-5-form-item-label{
            margin-right: 12px !important;
        }
        label{
            &: after{
                display: none !important;
            }
        }
    }

`;

const RightFilterWrapper = styled(Flex)`
    position: absolute;
    right: 0;
    bottom: 0;
`;

const items = [
    // {
    //     label: '精选',
    //     key: 'id1',
    // },
    {
        label: '全部',
        key: 'all',
    },
    {
        label: '我收藏的',
        key: 'favorite',
    },
    {
        label: '我发布的',
        key: 'isMine',
    },
];

export interface FilterValues {
    serverSourceType?: string;
    serverProtocolType?: string;
    labels?: number[];
    isLatest?: boolean;
    isHottest?: boolean;
    publishTimeDesc?: boolean;
    publishTimeAsc?: boolean;
    viewCountDesc?: boolean;
    viewCountAsc?: boolean;
}

export interface TabValues {
    tab: string;
    keywords?: string;
    favorite?: boolean;
    isMine?: boolean;
}

interface Props {
    initialTabFormValue: TabValues;
    initialFilterFormValue: FilterValues;
}
const SquireFilter = ({initialTabFormValue, initialFilterFormValue}: Props) => {
    const [tabForm] = Form.useForm();
    const [filterForm] = Form.useForm();

    return (
        <>
            <Form
                name="tab"
                form={tabForm}
                initialValues={initialTabFormValue}
            >
                <Form.Item name="tab" noStyle>
                    <TitleTabs
                        tabBarExtraContent={(
                            <Flex align="center" gap={8} style={{marginTop: 8}}>
                                <Form.Item name="keywords" noStyle>
                                    <Input
                                        autoFocus
                                        style={{width: 250}}
                                        placeholder="输入MCP Server名称"
                                        suffix={<SearchOutlined />}
                                        allowClear
                                    />
                                </Form.Item>
                                <CreateMCPButton />
                                <MCPPlaygoundButton />
                            </Flex>
                        )}
                        items={items}
                    />
                </Form.Item>
            </Form>
            <StyleForm
                colon={false}
                labelCol={{flex: '60px'}}
                labelAlign="left"
                form={filterForm}
                initialValues={initialFilterFormValue}
                name="filter"
            >
                <Flex vertical gap={8}>
                    <Form.Item name="serverSourceType" label="类型">
                        <Select
                            placeholder="全部类型"
                            style={{width: 120}}
                            options={[
                                {label: '全部类型', value: 'all'},
                                {label: '标准MCP', value: 'external'},
                                {label: 'Remote', value: 'openapi'},
                                {label: 'Local', value: 'script'},
                            ]}
                        />
                    </Form.Item>
                    <Form.Item name="serverProtocolType" label="协议">
                        <Select
                            placeholder="全部协议"
                            style={{width: 120}}
                            options={[
                                {label: '全部协议', value: 'all'},
                                {label: 'SSE', value: 'sse'},
                                {label: 'STDIO', value: 'stdio'},
                                {label: 'Streamable HTTP', value: 'streamable_http'},
                            ]}
                        />
                    </Form.Item>
                    <Form.Item name="labels" label="场景">
                        <LabelsFilterContent />
                    </Form.Item>
                    <RightFilterWrapper gap={16}>
                        <Form.Item name="isLatest" className="noLine" label="最新" valuePropName="checked">
                            <Switch size="small" />
                        </Form.Item>
                        <Form.Item name="isHottest" className="noLine" label="最热" valuePropName="checked">
                            <Switch size="small" />
                        </Form.Item>
                        <Form.Item className="noLine" label="综合排序">
                            <Popover
                                trigger="click"
                                placement="bottomLeft"
                                content={(
                                    <Space direction="vertical" size={8} style={{width: 160}}>
                                        <Form.Item
                                            name="publishTimeDesc"
                                            valuePropName="checked"
                                            style={{margin: 0}}
                                        >
                                            <Flex justify="space-between" align="center">
                                                <span>发布时间倒序</span>
                                                <Switch size="small" />
                                            </Flex>
                                        </Form.Item>
                                        <Form.Item
                                            name="publishTimeAsc"
                                            valuePropName="checked"
                                            style={{margin: 0}}
                                        >
                                            <Flex justify="space-between" align="center">
                                                <span>发布时间正序</span>
                                                <Switch size="small" />
                                            </Flex>
                                        </Form.Item>
                                        <Form.Item
                                            name="viewCountDesc"
                                            valuePropName="checked"
                                            style={{margin: 0}}
                                        >
                                            <Flex justify="space-between" align="center">
                                                <span>浏览量从高到低</span>
                                                <Switch size="small" />
                                            </Flex>
                                        </Form.Item>
                                        <Form.Item
                                            name="viewCountAsc"
                                            valuePropName="checked"
                                            style={{margin: 0}}
                                        >
                                            <Flex justify="space-between" align="center">
                                                <span>浏览量从低到高</span>
                                                <Switch size="small" />
                                            </Flex>
                                        </Form.Item>
                                    </Space>
                                )}
                            >
                                <div
                                    style={{
                                        cursor: 'pointer',
                                        padding: '4px 8px',
                                        border: '1px solid #d9d9d9',
                                        borderRadius: 4,
                                        display: 'flex',
                                        alignItems: 'center',
                                        gap: 4,
                                        minWidth: 80,
                                    }}
                                >
                                    <span>排序</span>
                                    <DownOutlined style={{fontSize: 12}} />
                                </div>
                            </Popover>
                        </Form.Item>
                    </RightFilterWrapper>
                </Flex>
            </StyleForm>
        </>
    );
};

export default SquireFilter;
